# TrackResults Web Application - Local Development Setup Guide

## 🚀 Quick Start - Running the Application

Once you've completed the setup below, use these commands to run the web application:

### Start the Web Application
```powershell
# Navigate to the project root directory
cd C:\Users\<USER>\source\TrackResults

# Start IIS Express on port 44300
& "C:\Program Files\IIS Express\iisexpress.exe" /path:"C:\Users\<USER>\source\TrackResults\Web" /port:44300 /clr:v4.0
```

### Access the Application
- **URL**: http://localhost:44300
- **Login Page**: http://localhost:44300/Security/Login.aspx
- To stop the server: Press `Q` in the IIS Express console

---

## 📋 Prerequisites

Before setting up the project, ensure you have the following installed:

1. **Visual Studio 2017 Enterprise** (or compatible version with MSBuild 15.0)
2. **.NET Framework 4.8 Developer Pack** - [Download Here](https://dotnet.microsoft.com/en-us/download/dotnet-framework/thank-you/net48-developer-pack-offline-installer)
3. **IIS Express** (usually comes with Visual Studio)
4. **Git** for version control

---

## 🔧 Setup Instructions

### Step 1: Install .NET Framework 4.8 Developer Pack

1. Download the .NET Framework 4.8 Developer Pack from the link above
2. Run the installer as Administrator
3. Complete the installation process
4. Restart your computer if prompted

### Step 2: Clone and Prepare the Repository

```powershell
# Clone the repository (if not already done)
git clone [repository-url]
cd TrackResults
```

### Step 3: Fix Project Configuration Issues

The following changes were made to resolve build issues:

#### A. Update BES Project C# Language Version

**File**: `BES/BES.csproj`
**Change**: Added C# 7.3 language version support

```xml
<PropertyGroup>
    <!-- ... existing properties ... -->
    <LangVersion>7.3</LangVersion>
    <!-- ... -->
</PropertyGroup>
```

#### B. Fix Web Project MSBuild Targets

**File**: `Web/Web.csproj`
**Changes**: Updated Visual Studio version and added fallback paths

```xml
<PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">15.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
</PropertyGroup>
<Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
<Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != '' And Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets')" />
<Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v15.0\WebApplications\Microsoft.WebApplication.targets" Condition="!Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets') And Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v15.0\WebApplications\Microsoft.WebApplication.targets')" />
<Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="!Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets') And !Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v15.0\WebApplications\Microsoft.WebApplication.targets')" />
```

#### C. Fix PostBuildEvent License File Copying

**File**: `Web/Web.csproj`
**Change**: Made license file copying conditional

```xml
<PropertyGroup>
    <PostBuildEvent>if exist "$(ProjectDir)..\Common\Libraries\ComponentArt.UIFramework.lic" copy /Y "$(ProjectDir)..\Common\Libraries\ComponentArt.UIFramework.lic" "$(TargetDir)"
if exist "$(ProjectDir)..\Common\Libraries\ExtendedObjectDataSource.lic" copy /Y "$(ProjectDir)..\Common\Libraries\ExtendedObjectDataSource.lic" "$(TargetDir)"</PostBuildEvent>
</PropertyGroup>
```

### Step 4: Restore NuGet Packages

```powershell
# Download NuGet.exe (if not already available)
Invoke-WebRequest -Uri "https://dist.nuget.org/win-x86-commandline/latest/nuget.exe" -OutFile "nuget.exe"

# Restore packages for the solution
dotnet restore FireFly.sln

# Restore packages for the Web project specifically
.\nuget.exe restore Web\packages.config -PackagesDirectory packages
```

### Step 5: Build the Solution

```powershell
# Build using MSBuild (adjust path if Visual Studio version differs)
& "C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin\MSBuild.exe" "Web\Web.csproj" /p:Configuration=Debug /p:Platform=AnyCPU
```

---

## 🗂️ Project Structure

```
TrackResults/
├── BES/                    # Business Entity Services
├── Common/                 # Shared libraries and utilities
├── Web/                    # Main web application
├── packages/               # NuGet packages (created after restore)
├── FireFly.sln            # Solution file
├── nuget.exe              # NuGet command line tool
└── SETUP_GUIDE.md         # This file
```

---

## 🔍 Troubleshooting

### Common Issues and Solutions

1. **"Reference assemblies for .NET Framework 4.8 not found"**
   - Ensure .NET Framework 4.8 Developer Pack is installed
   - Restart Visual Studio/VS Code after installation

2. **"WebApplication targets not found"**
   - Verify Visual Studio 2017 is installed with web development tools
   - Check MSBuild path in build commands

3. **NuGet package restore fails**
   - Ensure internet connection is available
   - Try deleting `packages` folder and running restore again

4. **IIS Express won't start**
   - Check if port 44300 is already in use: `netstat -ano | findstr :44300`
   - Try a different port number
   - Run as Administrator if needed

### Build Warnings

The following warnings are normal and don't prevent the application from running:
- CS0219: Variable assigned but never used
- CS0168: Variable declared but never used
- CS0169: Field never used
- CS0414: Field assigned but never used

---

## 📝 Configuration Notes

- **Database**: The application is configured to connect to Azure SQL Database
- **Environment**: Set to "Development" in web.config
- **Authentication**: Uses Forms authentication with SQL membership provider
- **Session**: Configured for InProc session state with 2880-minute timeout

---

## 🎯 Next Steps

After successful setup:
1. Configure database connection strings if needed
2. Set up user accounts and roles
3. Configure any required integrations
4. Review application settings in web.config

---

*Last updated: June 2025*
