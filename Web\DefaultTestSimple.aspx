<%@ Page Title="Enhanced Dashboard Test" Language="C#" MasterPageFile="~/MasterPages/Default.Master" AutoEventWireup="true" CodeBehind="DefaultTestSimple.aspx.cs" Inherits="TrackResults.Web.DefaultTestSimple" %>

<asp:Content ID="Content1" ContentPlaceHolderID="c1" runat="server">
    
    <div class="band band-white-space">
    <div class="container">
        <div class="row">
            <div class="col-xs-12">
                <h2>Enhanced Dashboard Test</h2>
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h3 class="panel-title">Test Status</h3>
                    </div>
                    <div class="panel-body">
                        <asp:Label ID="lblMessage" runat="server" Text="Loading..." CssClass="lead" />
                        <hr />
                        <p>This is a simple test page to verify that the enhanced dashboard structure is working correctly.</p>
                        <p>If you can see this message, the page is loading successfully and we can proceed with the full enhanced dashboard.</p>
                        
                        <div class="alert alert-info">
                            <strong>Next Steps:</strong>
                            <ul>
                                <li>Verify this test page loads correctly</li>
                                <li>Build the project in Visual Studio</li>
                                <li>Test the full DefaultTest.aspx page</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

</asp:Content>
